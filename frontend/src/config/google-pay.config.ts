/**
 * Centralized Google Pay configuration
 * This file consolidates all Google Pay related configuration to avoid duplication
 */

export type GooglePayEnvironment = "TEST" | "PRODUCTION";

export interface GooglePayConfig {
  enabled: boolean;
  merchantName: string;
  environment: GooglePayEnvironment;
  allowedCardNetworks?: Array<"VISA" | "MASTERCARD" | "AMEX" | "DISCOVER" | "JCB" | "INTERAC">;
  allowedCardAuthMethods?: Array<"PAN_ONLY" | "CRYPTOGRAM_3DS">;
  billingAddressRequired?: boolean;
  shippingAddressRequired?: boolean;
  phoneNumberRequired?: boolean;
}

/**
 * Determines the Google Pay environment based on the current environment
 * @returns The appropriate Google Pay environment setting
 */
export const getGooglePayEnvironment = (): GooglePayEnvironment => {
  const isDevelopment = import.meta.env.DEV;
  const isStaging = import.meta.env.VITE_ENV === "staging";
  const isProduction = import.meta.env.VITE_ENV === "production" || import.meta.env.PROD;

  // Use PRODUCTION for production environment, TEST for all others
  return isProduction ? "PRODUCTION" : "TEST";
};

/**
 * Default Google Pay configuration with environment-aware settings
 */
export const DEFAULT_GOOGLE_PAY_CONFIG: Partial<GooglePayConfig> = {
  enabled: true,
  environment: getGooglePayEnvironment(),
  allowedCardNetworks: ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
  allowedCardAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
  billingAddressRequired: true,
  shippingAddressRequired: false,
  phoneNumberRequired: false,
};

/**
 * Creates a complete Google Pay configuration
 * @param merchantName - The merchant's display name
 * @param overrides - Optional configuration overrides
 * @returns Complete Google Pay configuration
 */
export const createGooglePayConfig = (merchantName: string, overrides?: Partial<GooglePayConfig>): GooglePayConfig => {
  return {
    ...DEFAULT_GOOGLE_PAY_CONFIG,
    merchantName,
    ...overrides,
  } as GooglePayConfig;
};

/**
 * Validates Google Pay configuration
 * @param config - The configuration to validate
 * @returns True if valid, false otherwise
 */
export const isValidGooglePayConfig = (config: unknown): config is GooglePayConfig => {
  if (!config || typeof config !== "object") return false;

  const c = config as Record<string, unknown>;

  return (
    typeof c.enabled === "boolean" &&
    typeof c.merchantName === "string" &&
    c.merchantName.length > 0 &&
    (c.environment === "TEST" || c.environment === "PRODUCTION")
  );
};

/**
 * Updates the PayFields window object with Google Pay configuration
 * @param config - The Google Pay configuration
 */
export const applyGooglePayToPayFields = (config: GooglePayConfig): void => {
  if (!window.PayFields) {
    throw new Error("PayFields not loaded");
  }

  if (config.enabled) {
    if (!window.PayFields.config.googlePay) {
      window.PayFields.config.googlePay = {} as { enabled: boolean; environment: GooglePayEnvironment };
    }

    window.PayFields.config.googlePay.enabled = true;
    window.PayFields.config.googlePay.environment = config.environment;

    // Show Google Pay button container
    const googlePayContainer = document.getElementById("googlePayButton");
    if (googlePayContainer) {
      googlePayContainer.style.display = "block";
    }
  } else {
    // Hide Google Pay button if not enabled
    const googlePayContainer = document.getElementById("googlePayButton");
    if (googlePayContainer) {
      googlePayContainer.style.display = "none";
    }
  }
};
